# Test Plan: ActivityTypeOptions Refresh After TaskingStartUnderway Creation

## Issue Description
After a TaskingStartUnderway is created/saved, the activityTypeOptions is not refreshed. It should be refreshed so that the other tasking activity types (TaskingOnScene, TaskingOnTow, etc.) will show after a TaskingStartUnderway is created/saved.

## Solution Implemented
Added `fetchActivityTypes()` call to the `handleSetCurrentEventType` function in `sealogs-frontend/src/app/ui/logbook/events.tsx` (line 414).

## Manual Testing Steps

### Prerequisites
1. Start the SeaLogs frontend application
2. Navigate to a logbook entry page
3. Ensure you have appropriate permissions to create tasking events

### Test Steps

1. **Initial State Check**
   - Open the Events section of a logbook entry
   - Click on the "Activity Type" dropdown
   - Verify that "TaskingStartUnderway" is available in the options
   - Note which other tasking options are currently available

2. **Create TaskingStartUnderway**
   - Select "TaskingStartUnderway" from the Activity Type dropdown
   - Fill in the required fields in the Tasking form:
     - Time
     - Location (if required)
     - Any other mandatory fields
   - Click "Save" to create the TaskingStartUnderway event

3. **Verify Options Refresh**
   - After the modal closes and the event is saved
   - Click on the "Activity Type" dropdown again
   - **Expected Result**: Additional tasking activity types should now be available:
     - TaskingOnScene
     - TaskingOnTow
     - TaskingPaused
     - TaskingResumed
     - TaskingComplete
   - **Previous Behavior**: These options would not appear until page refresh

4. **Verify Functionality**
   - Try selecting one of the newly available options (e.g., "TaskingOnScene")
   - Verify that the appropriate form opens
   - This confirms that the options are not only visible but functional

### Expected Behavior After Fix

- ✅ Activity type options refresh automatically after TaskingStartUnderway creation
- ✅ No page refresh required to see additional tasking options
- ✅ User can immediately create follow-up tasking events (TaskingOnScene, etc.)
- ✅ Improved user experience with seamless workflow

### Fallback Test

If activity types don't load for any reason, there's a fallback "Refresh activity types" button that should appear. Test this by:

1. If the dropdown is empty or not loading
2. Look for a "Refresh activity types" button
3. Click it to manually refresh the options

## Code Changes Made

### File: `sealogs-frontend/src/app/ui/logbook/events.tsx`

**Function Modified**: `handleSetCurrentEventType` (lines 405-415)

**Change**: Added `fetchActivityTypes()` call on line 414

```javascript
const handleSetCurrentEventType = () => {
    setCurrentEventType(false)
    // Reset accordion state to properly close it
    setAccordionValue('')
    setSelectedRow(0)
    setCurrentEvent(false)
    setCurrentStop(false)
    // setOpenEventModal(false)
    // Refresh activity types to ensure TaskingStartUnderway creation updates available options
    fetchActivityTypes()  // <-- This line was added
}
```

**Explanation**: The `fetchActivityTypes()` function calls `initData()` which recalculates the available activity type options based on the current trip events and configuration. This ensures that after a TaskingStartUnderway is created, the additional tasking options become available immediately.

## Technical Details

The fix works by:

1. When a Tasking form is saved, it calls `updateTripReport()` to refresh the trip data
2. The form then calls `closeModal()` which is the `handleSetCurrentEventType` function
3. `handleSetCurrentEventType` now calls `fetchActivityTypes()`
4. `fetchActivityTypes()` calls `initData()` which recalculates activity options
5. The `useEffect` on lines 556-565 updates `activityTypeOptions` based on current events and trip data
6. The dropdown immediately shows the updated options

This creates a seamless user experience where tasking workflows can be completed without page refreshes.
